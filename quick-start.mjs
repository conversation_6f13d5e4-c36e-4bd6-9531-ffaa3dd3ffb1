#!/usr/bin/env node

/**
 * Quick Start Script for MEV Arbitrage Bot
 * ========================================
 * 
 * Simple startup script to test system functionality
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

console.log('🚀 MEV Arbitrage Bot - Quick Start');
console.log('==================================');

async function quickStart() {
  try {
    console.log('1. Checking Docker...');
    
    // Check Docker
    try {
      const { stdout } = await execAsync('docker --version');
      console.log('✅ Docker available:', stdout.trim());
    } catch (error) {
      console.log('❌ Docker not available:', error.message);
      return;
    }
    
    console.log('\n2. Starting database containers...');
    
    // Start Docker containers
    try {
      await execAsync('docker-compose up -d redis postgres influxdb');
      console.log('✅ Database containers started');
    } catch (error) {
      console.log('❌ Failed to start containers:', error.message);
    }
    
    console.log('\n3. Checking container status...');
    
    // Check container status
    try {
      const { stdout } = await execAsync('docker ps --filter "name=mev-" --format "table {{.Names}}\\t{{.Status}}"');
      console.log('Container Status:');
      console.log(stdout);
    } catch (error) {
      console.log('❌ Failed to check containers:', error.message);
    }
    
    console.log('\n4. Starting backend server...');
    
    // Start backend
    const backend = spawn('node', ['enhanced-backend.mjs'], {
      stdio: 'pipe'
    });
    
    backend.stdout.on('data', (data) => {
      console.log('Backend:', data.toString().trim());
    });
    
    backend.stderr.on('data', (data) => {
      console.log('Backend Error:', data.toString().trim());
    });
    
    backend.on('error', (error) => {
      console.log('❌ Backend failed to start:', error.message);
    });
    
    console.log('✅ Backend starting...');
    
    // Wait a bit for backend to start
    setTimeout(async () => {
      console.log('\n5. Testing API endpoints...');
      
      try {
        // Test health endpoint
        const response = await fetch('http://localhost:3001/api/health');
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health check:', data);
        } else {
          console.log('⚠️  Health check failed:', response.status);
        }
      } catch (error) {
        console.log('❌ API test failed:', error.message);
      }
      
      console.log('\n🎉 Quick start completed!');
      console.log('Frontend: http://localhost:3000');
      console.log('Backend API: http://localhost:3001');
      console.log('Health Check: http://localhost:3001/api/health');
      
    }, 10000); // Wait 10 seconds
    
  } catch (error) {
    console.log('💥 Quick start failed:', error.message);
  }
}

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

quickStart();
