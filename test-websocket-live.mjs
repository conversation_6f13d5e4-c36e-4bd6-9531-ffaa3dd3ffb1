#!/usr/bin/env node

/**
 * Live WebSocket Data Routing Test
 * Tests real-time data flow through WebSocket connections
 */

import { WebSocket } from 'ws';
import axios from 'axios';

const WS_URL = 'ws://localhost:8080/ws';
const API_BASE = 'http://localhost:3001';

// Test configuration
const TEST_DURATION = 10000; // 10 seconds
const LATENCY_TARGET = 5000; // 5 seconds max latency

console.log('🧪 WebSocket Live Data Routing Test');
console.log('===================================\n');

async function testWebSocketDataRouting() {
  return new Promise((resolve) => {
    const results = {
      connected: false,
      messagesReceived: 0,
      latencies: [],
      errors: [],
      dataTypes: new Set()
    };

    console.log(`📡 Connecting to WebSocket: ${WS_URL}`);
    const ws = new WebSocket(WS_URL);
    
    const startTime = Date.now();
    let testTimeout;

    ws.on('open', () => {
      console.log('✅ WebSocket connected successfully');
      results.connected = true;
      
      // Subscribe to all channels
      const subscriptions = [
        'opportunities',
        'trades', 
        'system:health',
        'prices',
        'analytics'
      ];
      
      subscriptions.forEach(channel => {
        ws.send(JSON.stringify({
          type: 'subscribe',
          channel: channel
        }));
        console.log(`📋 Subscribed to channel: ${channel}`);
      });

      // Set test timeout
      testTimeout = setTimeout(() => {
        ws.close();
      }, TEST_DURATION);
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        const receiveTime = Date.now();
        
        results.messagesReceived++;
        results.dataTypes.add(message.type || message.channel || 'unknown');
        
        // Calculate latency if timestamp is available
        if (message.timestamp) {
          const messageTime = new Date(message.timestamp).getTime();
          const latency = receiveTime - messageTime;
          results.latencies.push(latency);
          
          if (latency > LATENCY_TARGET) {
            console.log(`⚠️  High latency detected: ${latency}ms for ${message.type || message.channel}`);
          }
        }
        
        console.log(`📨 Received: ${message.type || message.channel} (${results.messagesReceived} total)`);
        
      } catch (error) {
        results.errors.push(`Parse error: ${error.message}`);
        console.log(`❌ Message parse error: ${error.message}`);
      }
    });

    ws.on('error', (error) => {
      results.errors.push(`WebSocket error: ${error.message}`);
      console.log(`❌ WebSocket error: ${error.message}`);
    });

    ws.on('close', () => {
      clearTimeout(testTimeout);
      console.log('\n📊 WebSocket Test Results:');
      console.log('==========================');
      console.log(`Connected: ${results.connected ? '✅' : '❌'}`);
      console.log(`Messages Received: ${results.messagesReceived}`);
      console.log(`Data Types: ${Array.from(results.dataTypes).join(', ')}`);
      
      if (results.latencies.length > 0) {
        const avgLatency = results.latencies.reduce((a, b) => a + b, 0) / results.latencies.length;
        const maxLatency = Math.max(...results.latencies);
        const minLatency = Math.min(...results.latencies);
        
        console.log(`Average Latency: ${avgLatency.toFixed(2)}ms`);
        console.log(`Min/Max Latency: ${minLatency}ms / ${maxLatency}ms`);
        console.log(`Latency Target Met: ${maxLatency <= LATENCY_TARGET ? '✅' : '❌'}`);
      }
      
      console.log(`Errors: ${results.errors.length}`);
      if (results.errors.length > 0) {
        results.errors.forEach(error => console.log(`  - ${error}`));
      }
      
      resolve(results);
    });
  });
}

async function testAPITriggers() {
  console.log('\n🔄 Testing API Triggers for Real-time Updates');
  console.log('==============================================');
  
  try {
    // Trigger real-time update
    console.log('📡 Triggering real-time update...');
    const response = await axios.get(`${API_BASE}/api/realtime/update`);
    console.log(`✅ Real-time update triggered: ${response.status}`);
    
    // Wait a moment for WebSocket messages
    await new Promise(resolve => setTimeout(resolve, 2000));
    
  } catch (error) {
    console.log(`❌ API trigger failed: ${error.message}`);
  }
}

async function runTests() {
  try {
    // Test API triggers first
    await testAPITriggers();
    
    // Test WebSocket data routing
    const wsResults = await testWebSocketDataRouting();
    
    // Final assessment
    console.log('\n🎯 Final Assessment:');
    console.log('====================');
    
    const passed = wsResults.connected && 
                   wsResults.messagesReceived > 0 && 
                   wsResults.errors.length === 0;
    
    console.log(`Overall Status: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Real-time Data Flow: ${wsResults.messagesReceived > 0 ? '✅ Working' : '❌ Not Working'}`);
    console.log(`WebSocket Performance: ${wsResults.latencies.length > 0 && Math.max(...wsResults.latencies) <= LATENCY_TARGET ? '✅ Meets Target' : '⚠️  Needs Review'}`);
    
  } catch (error) {
    console.log(`❌ Test execution failed: ${error.message}`);
  }
}

runTests();
