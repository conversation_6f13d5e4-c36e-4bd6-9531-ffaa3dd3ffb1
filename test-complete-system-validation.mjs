#!/usr/bin/env node

/**
 * Complete System Integration Validation
 * Comprehensive end-to-end test of the entire MEV arbitrage bot system
 */

import axios from 'axios';
import { WebSocket } from 'ws';

const API_BASE = 'http://localhost:3001';
const WS_URL = 'ws://localhost:8080/ws';

async function testCompleteSystemValidation() {
  console.log('🧪 Complete MEV Arbitrage Bot System Validation');
  console.log('===============================================\n');

  const results = {
    systemHealth: { score: 0, maxScore: 0, details: [] },
    coreServices: { operational: 0, total: 0, services: [] },
    dataFlow: { working: 0, total: 0, flows: [] },
    performance: { metrics: [], targets: [] },
    integration: { tests: [], passed: 0, failed: 0 }
  };

  try {
    // Test 1: System Health & Infrastructure
    console.log('🏥 Testing System Health & Infrastructure...');
    
    const healthTests = [
      {
        name: 'Backend Server',
        test: async () => {
          const response = await axios.get(`${API_BASE}/health`);
          return response.status === 200 && response.data.status === 'healthy';
        },
        weight: 10
      },
      {
        name: 'Database Connections',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/system/health`);
          const databases = response.data.data.databases;
          return databases.supabase && databases.influxdb && databases.redis;
        },
        weight: 15
      },
      {
        name: 'WebSocket Server',
        test: async () => {
          return new Promise((resolve) => {
            const ws = new WebSocket(WS_URL);
            const timeout = setTimeout(() => {
              ws.close();
              resolve(false);
            }, 3000);
            
            ws.on('open', () => {
              clearTimeout(timeout);
              ws.close();
              resolve(true);
            });
            
            ws.on('error', () => {
              clearTimeout(timeout);
              resolve(false);
            });
          });
        },
        weight: 10
      }
    ];

    for (const healthTest of healthTests) {
      try {
        const passed = await healthTest.test();
        results.systemHealth.details.push({
          name: healthTest.name,
          passed,
          weight: healthTest.weight
        });
        
        if (passed) {
          results.systemHealth.score += healthTest.weight;
        }
        results.systemHealth.maxScore += healthTest.weight;
        
        console.log(`  ${passed ? '✅' : '❌'} ${healthTest.name}`);
        
      } catch (error) {
        results.systemHealth.details.push({
          name: healthTest.name,
          passed: false,
          weight: healthTest.weight,
          error: error.message
        });
        results.systemHealth.maxScore += healthTest.weight;
        console.log(`  ❌ ${healthTest.name}: ${error.message}`);
      }
    }

    // Test 2: Core Services Validation
    console.log('\n🔧 Testing Core Services...');
    
    const coreServices = [
      { name: 'Opportunity Detection', endpoint: '/api/opportunities' },
      { name: 'Token Discovery', endpoint: '/api/tokens/top' },
      { name: 'Cross-chain Arbitrage', endpoint: '/api/opportunities/cross-chain' },
      { name: 'Analytics Engine', endpoint: '/api/analytics/performance' },
      { name: 'Network Management', endpoint: '/api/networks' },
      { name: 'Real-time Updates', endpoint: '/api/realtime/update' }
    ];

    for (const service of coreServices) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${API_BASE}${service.endpoint}`);
        const responseTime = Date.now() - startTime;
        
        const operational = response.status === 200 && response.data.success;
        
        results.coreServices.services.push({
          name: service.name,
          endpoint: service.endpoint,
          operational,
          responseTime,
          status: response.status
        });
        
        if (operational) results.coreServices.operational++;
        results.coreServices.total++;
        
        console.log(`  ${operational ? '✅' : '❌'} ${service.name}: ${response.status} (${responseTime}ms)`);
        
      } catch (error) {
        results.coreServices.services.push({
          name: service.name,
          endpoint: service.endpoint,
          operational: false,
          error: error.message
        });
        results.coreServices.total++;
        console.log(`  ❌ ${service.name}: ${error.message}`);
      }
    }

    // Test 3: Data Flow Validation
    console.log('\n🔄 Testing Data Flow Pipeline...');
    
    const dataFlows = [
      {
        name: 'Opportunity Detection → Storage',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/opportunities`);
          return response.data.success && Array.isArray(response.data.data);
        }
      },
      {
        name: 'Token Data → Cache → API',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/tokens/top?limit=5`);
          return response.data.success && response.data.data.length > 0;
        }
      },
      {
        name: 'Analytics → Aggregation → Display',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/analytics/performance`);
          return response.data.success && response.data.data.totalTrades !== undefined;
        }
      },
      {
        name: 'Real-time → WebSocket → Broadcast',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/realtime/update`);
          return response.data.success && response.data.data;
        }
      },
      {
        name: 'Cross-chain → Multi-network → Opportunities',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/opportunities/cross-chain`);
          return response.data.success;
        }
      }
    ];

    for (const flow of dataFlows) {
      try {
        const startTime = Date.now();
        const working = await flow.test();
        const duration = Date.now() - startTime;
        
        results.dataFlow.flows.push({
          name: flow.name,
          working,
          duration
        });
        
        if (working) results.dataFlow.working++;
        results.dataFlow.total++;
        
        console.log(`  ${working ? '✅' : '❌'} ${flow.name} (${duration}ms)`);
        
      } catch (error) {
        results.dataFlow.flows.push({
          name: flow.name,
          working: false,
          error: error.message
        });
        results.dataFlow.total++;
        console.log(`  ❌ ${flow.name}: ${error.message}`);
      }
    }

    // Test 4: Performance Validation
    console.log('\n⚡ Testing Performance Targets...');
    
    const performanceTests = [
      {
        name: 'API Response Time',
        target: 1000,
        unit: 'ms',
        test: async () => {
          const startTime = Date.now();
          await axios.get(`${API_BASE}/api/opportunities`);
          return Date.now() - startTime;
        }
      },
      {
        name: 'WebSocket Latency',
        target: 5000,
        unit: 'ms',
        test: async () => {
          return new Promise((resolve) => {
            const startTime = Date.now();
            const ws = new WebSocket(WS_URL);
            
            ws.on('open', () => {
              const latency = Date.now() - startTime;
              ws.close();
              resolve(latency);
            });
            
            ws.on('error', () => resolve(10000)); // Fail with high latency
          });
        }
      },
      {
        name: 'Database Query Time',
        target: 100,
        unit: 'ms',
        test: async () => {
          const startTime = Date.now();
          await axios.get(`${API_BASE}/api/tokens/top?limit=1`);
          return Date.now() - startTime;
        }
      }
    ];

    for (const perfTest of performanceTests) {
      try {
        const value = await perfTest.test();
        const passed = value <= perfTest.target;
        
        results.performance.metrics.push({
          name: perfTest.name,
          value,
          target: perfTest.target,
          unit: perfTest.unit,
          passed
        });
        
        console.log(`  ${passed ? '✅' : '❌'} ${perfTest.name}: ${value}${perfTest.unit} (target: <${perfTest.target}${perfTest.unit})`);
        
      } catch (error) {
        results.performance.metrics.push({
          name: perfTest.name,
          error: error.message,
          passed: false
        });
        console.log(`  ❌ ${perfTest.name}: ${error.message}`);
      }
    }

    // Test 5: End-to-End Integration
    console.log('\n🔗 Testing End-to-End Integration...');
    
    const integrationTests = [
      {
        name: 'Complete Arbitrage Workflow',
        description: 'Test opportunity detection → validation → execution pipeline',
        test: async () => {
          // Test the complete workflow
          const opportunities = await axios.get(`${API_BASE}/api/opportunities`);
          const analytics = await axios.get(`${API_BASE}/api/analytics/performance`);
          const health = await axios.get(`${API_BASE}/api/system/health`);
          
          return opportunities.data.success && 
                 analytics.data.success && 
                 health.data.success;
        }
      },
      {
        name: 'Multi-chain Integration',
        description: 'Test cross-chain arbitrage detection and network management',
        test: async () => {
          const networks = await axios.get(`${API_BASE}/api/networks`);
          const crossChain = await axios.get(`${API_BASE}/api/opportunities/cross-chain`);
          
          return networks.data.success && 
                 crossChain.data.success &&
                 networks.data.data.length > 0;
        }
      },
      {
        name: 'Real-time System Integration',
        description: 'Test real-time updates and WebSocket integration',
        test: async () => {
          const realtime = await axios.get(`${API_BASE}/api/realtime/update`);
          
          return new Promise((resolve) => {
            const ws = new WebSocket(WS_URL);
            let messageReceived = false;
            
            const timeout = setTimeout(() => {
              ws.close();
              resolve(realtime.data.success && messageReceived);
            }, 3000);
            
            ws.on('open', () => {
              ws.send(JSON.stringify({ type: 'subscribe', channel: 'opportunities' }));
            });
            
            ws.on('message', () => {
              messageReceived = true;
            });
            
            ws.on('error', () => {
              clearTimeout(timeout);
              resolve(false);
            });
          });
        }
      }
    ];

    for (const integrationTest of integrationTests) {
      try {
        const startTime = Date.now();
        const passed = await integrationTest.test();
        const duration = Date.now() - startTime;
        
        results.integration.tests.push({
          name: integrationTest.name,
          description: integrationTest.description,
          passed,
          duration
        });
        
        if (passed) results.integration.passed++;
        else results.integration.failed++;
        
        console.log(`  ${passed ? '✅' : '❌'} ${integrationTest.name} (${duration}ms)`);
        console.log(`    ${integrationTest.description}`);
        
      } catch (error) {
        results.integration.tests.push({
          name: integrationTest.name,
          description: integrationTest.description,
          passed: false,
          error: error.message
        });
        results.integration.failed++;
        console.log(`  ❌ ${integrationTest.name}: ${error.message}`);
      }
    }

    // Final System Assessment
    console.log('\n📊 Complete System Validation Summary:');
    console.log('======================================');
    
    const healthScore = (results.systemHealth.score / results.systemHealth.maxScore * 100).toFixed(1);
    const serviceScore = (results.coreServices.operational / results.coreServices.total * 100).toFixed(1);
    const dataFlowScore = (results.dataFlow.working / results.dataFlow.total * 100).toFixed(1);
    const performanceScore = (results.performance.metrics.filter(m => m.passed).length / results.performance.metrics.length * 100).toFixed(1);
    const integrationScore = (results.integration.passed / (results.integration.passed + results.integration.failed) * 100).toFixed(1);
    
    console.log(`🏥 System Health: ${healthScore}% (${results.systemHealth.score}/${results.systemHealth.maxScore})`);
    console.log(`🔧 Core Services: ${serviceScore}% (${results.coreServices.operational}/${results.coreServices.total})`);
    console.log(`🔄 Data Flow: ${dataFlowScore}% (${results.dataFlow.working}/${results.dataFlow.total})`);
    console.log(`⚡ Performance: ${performanceScore}% (${results.performance.metrics.filter(m => m.passed).length}/${results.performance.metrics.length})`);
    console.log(`🔗 Integration: ${integrationScore}% (${results.integration.passed}/${results.integration.passed + results.integration.failed})`);
    
    const overallScore = (parseFloat(healthScore) + parseFloat(serviceScore) + parseFloat(dataFlowScore) + parseFloat(performanceScore) + parseFloat(integrationScore)) / 5;
    
    console.log(`\n🎯 Overall System Score: ${overallScore.toFixed(1)}%`);
    
    if (overallScore >= 90) {
      console.log('🎉 EXCELLENT: MEV Arbitrage Bot system is fully operational!');
    } else if (overallScore >= 80) {
      console.log('✅ GOOD: MEV Arbitrage Bot system is operational with minor issues');
    } else if (overallScore >= 70) {
      console.log('⚠️  ACCEPTABLE: MEV Arbitrage Bot system needs attention');
    } else {
      console.log('❌ CRITICAL: MEV Arbitrage Bot system requires immediate fixes');
    }
    
    console.log('\n📈 Key Performance Metrics:');
    results.performance.metrics.forEach(metric => {
      if (metric.value !== undefined) {
        console.log(`  • ${metric.name}: ${metric.value}${metric.unit} ${metric.passed ? '✅' : '❌'}`);
      }
    });

  } catch (error) {
    console.error('❌ Complete system validation failed:', error.message);
  }
}

testCompleteSystemValidation();
