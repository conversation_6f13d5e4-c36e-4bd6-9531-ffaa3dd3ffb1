#!/usr/bin/env node

/**
 * Multi-Database Architecture Validation Test
 * Tests data routing between Supabase, InfluxDB, Redis, and PostgreSQL
 */

import axios from 'axios';
import { createClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import Redis from 'redis';

const API_BASE = 'http://localhost:3001';

// Database configurations from .env
const SUPABASE_URL = 'https://sbgeliidkalbnywvaiwe.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.QxQSP8fRq4UNFA9VmkR_-cxp7H_TyYgGQwwofBsZRZc';
const INFLUXDB_URL = 'http://localhost:8086';
const INFLUXDB_TOKEN = '0yplGOzAMMkssXmfL_kD40Ey78LZ8_De2RbvK5Z-s-bIMZPbU_F2k-ZB3_jo-wpUbPZhsf3VDcgGwu3jwy5Ctw==';
const REDIS_URL = 'redis://localhost:6379';

async function testMultiDatabaseArchitecture() {
  console.log('🧪 Multi-Database Architecture Validation');
  console.log('==========================================\n');

  const results = {
    supabase: { connected: false, operations: [] },
    influxdb: { connected: false, operations: [] },
    redis: { connected: false, operations: [] },
    dataRouting: { tests: [], passed: 0, failed: 0 }
  };

  try {
    // Test Supabase connection
    console.log('📊 Testing Supabase connection...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    try {
      const { data, error } = await supabase.from('arbitrage_opportunities').select('*').limit(1);
      results.supabase.connected = !error;
      results.supabase.operations.push('read_test');
      console.log('✅ Supabase connection successful');
    } catch (error) {
      console.log('⚠️  Supabase connection failed:', error.message);
    }

    // Test InfluxDB connection
    console.log('📈 Testing InfluxDB connection...');
    try {
      const influxDB = new InfluxDB({ url: INFLUXDB_URL, token: INFLUXDB_TOKEN });
      const queryApi = influxDB.getQueryApi('Dev-KE');
      
      const query = 'from(bucket: "mev-arbitrage-metrics") |> range(start: -1h) |> limit(n: 1)';
      const result = await queryApi.collectRows(query);
      
      results.influxdb.connected = true;
      results.influxdb.operations.push('query_test');
      console.log('✅ InfluxDB connection successful');
    } catch (error) {
      console.log('⚠️  InfluxDB connection failed:', error.message);
    }

    // Test Redis connection
    console.log('🔄 Testing Redis connection...');
    try {
      const redis = Redis.createClient({ url: REDIS_URL });
      await redis.connect();
      
      await redis.set('test_key', 'test_value', { EX: 10 });
      const value = await redis.get('test_key');
      
      results.redis.connected = value === 'test_value';
      results.redis.operations.push('read_write_test');
      
      await redis.disconnect();
      console.log('✅ Redis connection successful');
    } catch (error) {
      console.log('⚠️  Redis connection failed:', error.message);
    }

    // Test data routing patterns through API
    console.log('\n🔄 Testing Data Routing Patterns...');
    
    const routingTests = [
      {
        name: 'Opportunity Detection Flow',
        description: 'Test opportunity data routing to Supabase + Redis cache',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/opportunities`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'Price Data Flow',
        description: 'Test price data routing to InfluxDB + Redis cache',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/tokens/top`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'Analytics Data Flow',
        description: 'Test analytics data aggregation from multiple sources',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/analytics/performance`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'System Health Flow',
        description: 'Test system health data routing to InfluxDB',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/system/health`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'Real-time Update Flow',
        description: 'Test real-time data caching and WebSocket broadcasting',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/realtime/update`);
          return response.status === 200 && response.data.success;
        }
      }
    ];

    for (const routingTest of routingTests) {
      try {
        console.log(`🔍 Testing: ${routingTest.name}`);
        const startTime = Date.now();
        const passed = await routingTest.test();
        const duration = Date.now() - startTime;
        
        results.dataRouting.tests.push({
          name: routingTest.name,
          passed,
          duration,
          description: routingTest.description
        });
        
        if (passed) {
          results.dataRouting.passed++;
          console.log(`  ✅ ${routingTest.name} (${duration}ms)`);
        } else {
          results.dataRouting.failed++;
          console.log(`  ❌ ${routingTest.name} (${duration}ms)`);
        }
      } catch (error) {
        results.dataRouting.failed++;
        results.dataRouting.tests.push({
          name: routingTest.name,
          passed: false,
          error: error.message,
          description: routingTest.description
        });
        console.log(`  ❌ ${routingTest.name} - Error: ${error.message}`);
      }
    }

    // Performance validation
    console.log('\n⚡ Performance Validation...');
    const performanceTargets = {
      'Database Query Time': { target: 100, unit: 'ms' },
      'Cache Hit Ratio': { target: 80, unit: '%' },
      'API Response Time': { target: 1000, unit: 'ms' },
      'WebSocket Latency': { target: 5000, unit: 'ms' }
    };

    const avgResponseTime = results.dataRouting.tests
      .filter(t => t.duration)
      .reduce((sum, t) => sum + t.duration, 0) / 
      results.dataRouting.tests.filter(t => t.duration).length;

    console.log(`Average API Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`Performance Target (<1000ms): ${avgResponseTime < 1000 ? '✅' : '❌'}`);

    // Final summary
    console.log('\n📊 Multi-Database Architecture Summary:');
    console.log('========================================');
    console.log(`Supabase Connection: ${results.supabase.connected ? '✅' : '❌'}`);
    console.log(`InfluxDB Connection: ${results.influxdb.connected ? '✅' : '❌'}`);
    console.log(`Redis Connection: ${results.redis.connected ? '✅' : '❌'}`);
    console.log(`Data Routing Tests: ${results.dataRouting.passed}/${results.dataRouting.tests.length} passed`);
    console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    
    const overallSuccess = results.supabase.connected && 
                          results.influxdb.connected && 
                          results.redis.connected && 
                          results.dataRouting.passed >= results.dataRouting.tests.length * 0.8;
    
    console.log(`\nOverall Status: ${overallSuccess ? '✅ PASSED' : '❌ NEEDS ATTENTION'}`);

  } catch (error) {
    console.error('❌ Multi-database architecture test failed:', error.message);
  }
}

testMultiDatabaseArchitecture();
