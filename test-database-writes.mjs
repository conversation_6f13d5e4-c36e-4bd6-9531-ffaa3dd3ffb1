#!/usr/bin/env node

/**
 * Test Database Write Operations
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3001';

async function testDatabaseWrites() {
  console.log('🧪 Testing Database Write Operations');
  console.log('====================================\n');

  try {
    // Test backend availability first
    console.log('📡 Checking backend availability...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Backend is available\n');

    // Test opportunity write
    console.log('📝 Testing opportunity write...');
    const opportunityData = {
      token_pair: 'ETH/USDC',
      profit_usd: 125.50,
      profit_margin: 2.5,
      confidence: 85,
      source_exchange: 'Uniswap',
      target_exchange: 'SushiSwap',
      network: 'ethereum'
    };

    const oppResponse = await axios.post(`${API_BASE}/api/opportunities`, opportunityData);
    console.log('✅ Opportunity write successful:', oppResponse.data.data.id);

    // Test trade write
    console.log('📝 Testing trade write...');
    const tradeData = {
      opportunity_id: oppResponse.data.data.id,
      amount_usd: 1000,
      profit_usd: 25.75,
      gas_cost: 15.25,
      status: 'completed'
    };

    const tradeResponse = await axios.post(`${API_BASE}/api/trades`, tradeData);
    console.log('✅ Trade write successful:', tradeResponse.data.data.id);

    console.log('\n🎉 All database write operations successful!');
    console.log('📊 Results:');
    console.log(`   - Opportunity ID: ${oppResponse.data.data.id}`);
    console.log(`   - Trade ID: ${tradeResponse.data.data.id}`);

  } catch (error) {
    console.error('❌ Database write test failed:', error.response?.data || error.message);
    console.error('Full error:', error);
  }
}

testDatabaseWrites();
