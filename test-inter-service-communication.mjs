#!/usr/bin/env node

/**
 * Inter-Service Communication Test
 * Tests event-driven architecture and service integration
 */

import axios from 'axios';
import { WebSocket } from 'ws';

const API_BASE = 'http://localhost:3001';
const WS_URL = 'ws://localhost:8080/ws';

async function testInterServiceCommunication() {
  console.log('🧪 Inter-Service Communication Testing');
  console.log('======================================\n');

  const results = {
    apiServices: [],
    eventFlow: [],
    webSocketEvents: [],
    serviceIntegration: { passed: 0, failed: 0 }
  };

  try {
    // Test 1: API Service Availability
    console.log('🔍 Testing API Service Availability...');
    const apiEndpoints = [
      { name: 'Health Check', path: '/health' },
      { name: 'System Health', path: '/api/system/health' },
      { name: 'Opportunities', path: '/api/opportunities' },
      { name: 'Trades', path: '/api/trades' },
      { name: 'Tokens', path: '/api/tokens' },
      { name: 'Analytics', path: '/api/analytics/performance' },
      { name: 'Cross-chain', path: '/api/opportunities/cross-chain' },
      { name: 'Networks', path: '/api/networks' }
    ];

    for (const endpoint of apiEndpoints) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${API_BASE}${endpoint.path}`);
        const duration = Date.now() - startTime;
        
        const success = response.status === 200;
        results.apiServices.push({
          name: endpoint.name,
          path: endpoint.path,
          status: response.status,
          duration,
          success
        });
        
        console.log(`  ${success ? '✅' : '❌'} ${endpoint.name}: ${response.status} (${duration}ms)`);
        
        if (success) results.serviceIntegration.passed++;
        else results.serviceIntegration.failed++;
        
      } catch (error) {
        results.apiServices.push({
          name: endpoint.name,
          path: endpoint.path,
          error: error.message,
          success: false
        });
        results.serviceIntegration.failed++;
        console.log(`  ❌ ${endpoint.name}: Error - ${error.message}`);
      }
    }

    // Test 2: Event-Driven Data Flow
    console.log('\n🔄 Testing Event-Driven Data Flow...');
    
    const dataFlowTests = [
      {
        name: 'Real-time Update Trigger',
        description: 'Test triggering real-time updates and event propagation',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/realtime/update`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'Cross-chain Data Integration',
        description: 'Test cross-chain service data integration',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/analytics/cross-chain`);
          return response.status === 200 && response.data.success;
        }
      },
      {
        name: 'Token Data Synchronization',
        description: 'Test token service data synchronization',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/tokens/top?limit=10`);
          return response.status === 200 && response.data.success && response.data.data.length > 0;
        }
      }
    ];

    for (const flowTest of dataFlowTests) {
      try {
        console.log(`🔍 Testing: ${flowTest.name}`);
        const startTime = Date.now();
        const passed = await flowTest.test();
        const duration = Date.now() - startTime;
        
        results.eventFlow.push({
          name: flowTest.name,
          passed,
          duration,
          description: flowTest.description
        });
        
        if (passed) {
          results.serviceIntegration.passed++;
          console.log(`  ✅ ${flowTest.name} (${duration}ms)`);
        } else {
          results.serviceIntegration.failed++;
          console.log(`  ❌ ${flowTest.name} (${duration}ms)`);
        }
      } catch (error) {
        results.eventFlow.push({
          name: flowTest.name,
          passed: false,
          error: error.message,
          description: flowTest.description
        });
        results.serviceIntegration.failed++;
        console.log(`  ❌ ${flowTest.name} - Error: ${error.message}`);
      }
    }

    // Test 3: WebSocket Event Broadcasting
    console.log('\n📡 Testing WebSocket Event Broadcasting...');
    
    await new Promise((resolve) => {
      const ws = new WebSocket(WS_URL);
      let eventCount = 0;
      const timeout = setTimeout(() => {
        ws.close();
        resolve();
      }, 5000);

      ws.on('open', () => {
        console.log('  📡 WebSocket connected');
        
        // Subscribe to channels
        const channels = ['opportunities', 'trades', 'system:health', 'prices'];
        channels.forEach(channel => {
          ws.send(JSON.stringify({ type: 'subscribe', channel }));
        });
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          eventCount++;
          
          results.webSocketEvents.push({
            type: message.type || message.channel,
            timestamp: new Date().toISOString(),
            hasData: !!message.data
          });
          
          console.log(`  📨 Event received: ${message.type || message.channel} (#${eventCount})`);
          
          if (eventCount >= 3) {
            clearTimeout(timeout);
            ws.close();
            resolve();
          }
        } catch (error) {
          console.log(`  ❌ WebSocket message parse error: ${error.message}`);
        }
      });

      ws.on('error', (error) => {
        console.log(`  ❌ WebSocket error: ${error.message}`);
        clearTimeout(timeout);
        resolve();
      });

      ws.on('close', () => {
        console.log(`  📡 WebSocket closed (${eventCount} events received)`);
        clearTimeout(timeout);
        resolve();
      });
    });

    // Test 4: Service Integration Health
    console.log('\n🏥 Testing Service Integration Health...');
    
    try {
      const healthResponse = await axios.get(`${API_BASE}/api/system/health`);
      const healthData = healthResponse.data.data;
      
      console.log('  📊 Database Services:');
      Object.entries(healthData.databases).forEach(([db, status]) => {
        console.log(`    ${status ? '✅' : '❌'} ${db}: ${status ? 'Connected' : 'Disconnected'}`);
      });
      
      console.log('  📈 System Metrics:');
      console.log(`    Uptime: ${healthData.uptime.toFixed(2)}s`);
      console.log(`    Daily P&L: $${healthData.riskMetrics.dailyPnL}`);
      console.log(`    Win Rate: ${healthData.riskMetrics.winRate}%`);
      
    } catch (error) {
      console.log(`  ❌ Health check failed: ${error.message}`);
    }

    // Final Summary
    console.log('\n📊 Inter-Service Communication Summary:');
    console.log('=======================================');
    
    const totalTests = results.serviceIntegration.passed + results.serviceIntegration.failed;
    const successRate = totalTests > 0 ? (results.serviceIntegration.passed / totalTests * 100).toFixed(1) : 0;
    
    console.log(`API Services: ${results.apiServices.filter(s => s.success).length}/${results.apiServices.length} operational`);
    console.log(`Event Flow Tests: ${results.eventFlow.filter(e => e.passed).length}/${results.eventFlow.length} passed`);
    console.log(`WebSocket Events: ${results.webSocketEvents.length} events received`);
    console.log(`Overall Success Rate: ${successRate}%`);
    
    const avgResponseTime = results.apiServices
      .filter(s => s.duration)
      .reduce((sum, s) => sum + s.duration, 0) / 
      results.apiServices.filter(s => s.duration).length;
    
    console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    
    const overallSuccess = successRate >= 80 && avgResponseTime < 1000;
    console.log(`\nOverall Status: ${overallSuccess ? '✅ PASSED' : '❌ NEEDS ATTENTION'}`);
    
    if (overallSuccess) {
      console.log('🎉 Inter-service communication is working correctly!');
      console.log('📡 Event-driven architecture is operational');
      console.log('⚡ Performance targets are being met');
    }

  } catch (error) {
    console.error('❌ Inter-service communication test failed:', error.message);
  }
}

testInterServiceCommunication();
