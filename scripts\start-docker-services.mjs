#!/usr/bin/env node

/**
 * Docker Services Startup Script for MEV Arbitrage Bot
 * ====================================================
 * 
 * This script ensures proper Docker service startup with:
 * 1. Docker Desktop availability check
 * 2. Service startup in correct order: Redis → PostgreSQL → InfluxDB
 * 3. Health checks for each service
 * 4. Comprehensive error handling and retry logic
 * 5. Service status monitoring
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Service configuration
const services = {
  redis: {
    name: 'Redis',
    container: 'mev-redis',
    port: 6379,
    healthCheck: 'redis-cli ping',
    maxWaitTime: 30000
  },
  postgres: {
    name: 'PostgreSQL',
    container: 'mev-postgres',
    port: 5432,
    healthCheck: 'pg_isready -U mev_user -d mev_arbitrage_bot',
    maxWaitTime: 45000
  },
  influxdb: {
    name: 'InfluxDB',
    container: 'mev-influxdb',
    port: 8086,
    healthCheck: 'influx ping',
    maxWaitTime: 60000
  }
};

// Logging functions
function log(message, color = 'reset') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(step, message) {
  log(`\n🔄 Step ${step}: ${message}`, 'cyan');
}

// Check if Docker Desktop is running
async function checkDockerAvailability() {
  logStep(1, 'Checking Docker Desktop Availability');
  
  try {
    await execAsync('docker info');
    logSuccess('Docker Desktop is running');
    return true;
  } catch (error) {
    logError('Docker Desktop is not running or not installed');
    logInfo('Please start Docker Desktop and try again');
    return false;
  }
}

// Check if a service is already running
async function isServiceRunning(serviceName) {
  try {
    const { stdout } = await execAsync(`docker ps --filter "name=${services[serviceName].container}" --format "{{.Names}}"`);
    return stdout.trim() === services[serviceName].container;
  } catch (error) {
    return false;
  }
}

// Wait for service to be healthy
async function waitForServiceHealth(serviceName, maxWaitTime = 30000) {
  const service = services[serviceName];
  const startTime = Date.now();
  
  logInfo(`Waiting for ${service.name} to be healthy...`);
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const { stdout } = await execAsync(`docker exec ${service.container} ${service.healthCheck}`);
      if (stdout.includes('PONG') || stdout.includes('accepting connections') || stdout.includes('OK')) {
        logSuccess(`${service.name} is healthy`);
        return true;
      }
    } catch (error) {
      // Service not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  logWarning(`${service.name} health check timeout after ${maxWaitTime}ms`);
  return false;
}

// Start Docker services
async function startDockerServices() {
  logStep(2, 'Starting Docker Services');
  
  try {
    // Check if services are already running
    const runningServices = [];
    for (const [key, service] of Object.entries(services)) {
      if (await isServiceRunning(key)) {
        runningServices.push(service.name);
      }
    }
    
    if (runningServices.length > 0) {
      logInfo(`Already running: ${runningServices.join(', ')}`);
    }
    
    // Start all database services
    logInfo('Starting database containers...');
    await execAsync('docker-compose up -d redis postgres influxdb');
    logSuccess('Docker containers started');
    
    return true;
  } catch (error) {
    logError(`Failed to start Docker services: ${error.message}`);
    return false;
  }
}

// Verify service health
async function verifyServiceHealth() {
  logStep(3, 'Verifying Service Health');
  
  const healthResults = {};
  
  // Check services in startup order
  for (const [key, service] of Object.entries(services)) {
    logInfo(`Checking ${service.name} health...`);
    healthResults[key] = await waitForServiceHealth(key, service.maxWaitTime);
  }
  
  // Display results
  logInfo('\n📊 Service Health Summary:');
  console.log('================================');
  
  let allHealthy = true;
  for (const [key, service] of Object.entries(services)) {
    const status = healthResults[key] ? '✅ Healthy' : '❌ Unhealthy';
    console.log(`${service.name.padEnd(12)}: ${status}`);
    if (!healthResults[key]) allHealthy = false;
  }
  
  return allHealthy;
}

// Display service information
async function displayServiceInfo() {
  logStep(4, 'Service Information');
  
  console.log('\n🔗 Service Endpoints:');
  console.log('================================');
  console.log(`Redis:      redis://localhost:${services.redis.port}`);
  console.log(`PostgreSQL: postgresql://mev_user:mev_password@localhost:${services.postgres.port}/mev_arbitrage_bot`);
  console.log(`InfluxDB:   http://localhost:${services.influxdb.port}`);
  
  console.log('\n📋 Container Status:');
  try {
    const { stdout } = await execAsync('docker ps --filter "name=mev-" --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"');
    console.log(stdout);
  } catch (error) {
    logWarning('Could not retrieve container status');
  }
}

// Main startup function
async function startServices() {
  try {
    log(`${colors.bright}🐳 MEV ARBITRAGE BOT - DOCKER SERVICES STARTUP${colors.reset}`);
    log('=' .repeat(55));
    
    // Step 1: Check Docker availability
    if (!(await checkDockerAvailability())) {
      process.exit(1);
    }
    
    // Step 2: Start Docker services
    if (!(await startDockerServices())) {
      process.exit(1);
    }
    
    // Step 3: Verify service health
    const allHealthy = await verifyServiceHealth();
    
    // Step 4: Display service information
    await displayServiceInfo();
    
    if (allHealthy) {
      logSuccess('\n🎉 All Docker services are running and healthy!');
      logInfo('You can now start the backend with: npm run start:enhanced');
    } else {
      logWarning('\n⚠️  Some services may not be fully ready. Check the logs above.');
      logInfo('Services may still be initializing. Wait a moment and try again.');
    }
    
  } catch (error) {
    logError(`Docker services startup failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  log('\n🛑 Shutting down...');
  process.exit(0);
});

// Start the services
startServices();
