#!/usr/bin/env node

/**
 * Frontend Integration Test
 * Tests frontend dashboard components with live backend
 */

import axios from 'axios';
import { promises as fs } from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:3001'; // Backend serves static files

async function testFrontendIntegration() {
  console.log('🧪 Frontend Integration Testing');
  console.log('================================\n');

  const results = {
    staticFiles: [],
    apiIntegration: [],
    dataFlow: [],
    realTimeFeatures: [],
    performance: { loadTimes: [], apiCalls: [] }
  };

  try {
    // Test 1: Static File Serving
    console.log('📁 Testing Static File Serving...');
    
    const staticFiles = [
      { name: 'Main Dashboard', path: '/', contentType: 'text/html' },
      { name: 'Index HTML', path: '/index.html', contentType: 'text/html' }
    ];

    for (const file of staticFiles) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${FRONTEND_URL}${file.path}`);
        const loadTime = Date.now() - startTime;
        
        const success = response.status === 200 && 
                       response.headers['content-type']?.includes('html');
        
        results.staticFiles.push({
          name: file.name,
          path: file.path,
          status: response.status,
          loadTime,
          success,
          size: response.data.length
        });
        
        results.performance.loadTimes.push(loadTime);
        
        console.log(`  ${success ? '✅' : '❌'} ${file.name}: ${response.status} (${loadTime}ms, ${(response.data.length/1024).toFixed(1)}KB)`);
        
      } catch (error) {
        results.staticFiles.push({
          name: file.name,
          path: file.path,
          error: error.message,
          success: false
        });
        console.log(`  ❌ ${file.name}: Error - ${error.message}`);
      }
    }

    // Test 2: API Integration Points
    console.log('\n🔌 Testing API Integration Points...');
    
    const apiEndpoints = [
      { name: 'Dashboard Data', path: '/api/opportunities', expectedFields: ['success', 'data'] },
      { name: 'Token Data', path: '/api/tokens/top', expectedFields: ['success', 'data'] },
      { name: 'Analytics Data', path: '/api/analytics/performance', expectedFields: ['success', 'data'] },
      { name: 'System Health', path: '/api/system/health', expectedFields: ['success', 'data'] },
      { name: 'Cross-chain Data', path: '/api/opportunities/cross-chain', expectedFields: ['success', 'data'] }
    ];

    for (const endpoint of apiEndpoints) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${API_BASE}${endpoint.path}`);
        const responseTime = Date.now() - startTime;
        
        const hasExpectedFields = endpoint.expectedFields.every(field => 
          response.data.hasOwnProperty(field)
        );
        
        const success = response.status === 200 && hasExpectedFields;
        
        results.apiIntegration.push({
          name: endpoint.name,
          path: endpoint.path,
          status: response.status,
          responseTime,
          success,
          dataSize: JSON.stringify(response.data).length,
          hasExpectedFields
        });
        
        results.performance.apiCalls.push(responseTime);
        
        console.log(`  ${success ? '✅' : '❌'} ${endpoint.name}: ${response.status} (${responseTime}ms)`);
        
      } catch (error) {
        results.apiIntegration.push({
          name: endpoint.name,
          path: endpoint.path,
          error: error.message,
          success: false
        });
        console.log(`  ❌ ${endpoint.name}: Error - ${error.message}`);
      }
    }

    // Test 3: Data Flow Validation
    console.log('\n🔄 Testing Data Flow Validation...');
    
    const dataFlowTests = [
      {
        name: 'Opportunity Data Structure',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/opportunities`);
          const data = response.data.data;
          return Array.isArray(data) && data.length > 0 && 
                 data[0].hasOwnProperty('token_pair') && 
                 data[0].hasOwnProperty('profit_usd');
        }
      },
      {
        name: 'Token Data Structure',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/tokens/top?limit=5`);
          const data = response.data.data;
          return Array.isArray(data) && data.length > 0 && 
                 data[0].hasOwnProperty('symbol') && 
                 data[0].hasOwnProperty('price');
        }
      },
      {
        name: 'Analytics Data Structure',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/analytics/performance`);
          const data = response.data.data;
          return data.hasOwnProperty('totalTrades') && 
                 data.hasOwnProperty('totalProfit') && 
                 data.hasOwnProperty('successRate');
        }
      },
      {
        name: 'Real-time Update Trigger',
        test: async () => {
          const response = await axios.get(`${API_BASE}/api/realtime/update`);
          return response.data.success && response.data.data;
        }
      }
    ];

    for (const dataTest of dataFlowTests) {
      try {
        const startTime = Date.now();
        const passed = await dataTest.test();
        const duration = Date.now() - startTime;
        
        results.dataFlow.push({
          name: dataTest.name,
          passed,
          duration
        });
        
        console.log(`  ${passed ? '✅' : '❌'} ${dataTest.name} (${duration}ms)`);
        
      } catch (error) {
        results.dataFlow.push({
          name: dataTest.name,
          passed: false,
          error: error.message
        });
        console.log(`  ❌ ${dataTest.name}: Error - ${error.message}`);
      }
    }

    // Test 4: Real-time Features
    console.log('\n📡 Testing Real-time Features...');
    
    try {
      // Test WebSocket endpoint availability
      const wsTest = await axios.get(`${API_BASE}/api/realtime/update`);
      const wsAvailable = wsTest.status === 200;
      
      results.realTimeFeatures.push({
        name: 'WebSocket Endpoint',
        available: wsAvailable,
        status: wsTest.status
      });
      
      console.log(`  ${wsAvailable ? '✅' : '❌'} WebSocket Endpoint: ${wsTest.status}`);
      
      // Test real-time data updates
      const updateTest = await axios.get(`${API_BASE}/api/realtime/update`);
      const updatesWorking = updateTest.data.success;
      
      results.realTimeFeatures.push({
        name: 'Real-time Updates',
        working: updatesWorking,
        data: updateTest.data.data
      });
      
      console.log(`  ${updatesWorking ? '✅' : '❌'} Real-time Updates: ${updatesWorking ? 'Working' : 'Failed'}`);
      
    } catch (error) {
      console.log(`  ❌ Real-time features test failed: ${error.message}`);
    }

    // Performance Analysis
    console.log('\n⚡ Performance Analysis...');
    
    const avgLoadTime = results.performance.loadTimes.length > 0 ? 
      results.performance.loadTimes.reduce((a, b) => a + b, 0) / results.performance.loadTimes.length : 0;
    
    const avgApiTime = results.performance.apiCalls.length > 0 ? 
      results.performance.apiCalls.reduce((a, b) => a + b, 0) / results.performance.apiCalls.length : 0;
    
    console.log(`  📊 Average Page Load Time: ${avgLoadTime.toFixed(2)}ms`);
    console.log(`  📊 Average API Response Time: ${avgApiTime.toFixed(2)}ms`);
    console.log(`  📊 Load Time Target (<1000ms): ${avgLoadTime < 1000 ? '✅' : '❌'}`);
    console.log(`  📊 API Time Target (<500ms): ${avgApiTime < 500 ? '✅' : '❌'}`);

    // Final Summary
    console.log('\n📊 Frontend Integration Summary:');
    console.log('=================================');
    
    const staticFilesSuccess = results.staticFiles.filter(f => f.success).length;
    const apiIntegrationSuccess = results.apiIntegration.filter(a => a.success).length;
    const dataFlowSuccess = results.dataFlow.filter(d => d.passed).length;
    const realTimeSuccess = results.realTimeFeatures.filter(r => r.available || r.working).length;
    
    console.log(`Static Files: ${staticFilesSuccess}/${results.staticFiles.length} served`);
    console.log(`API Integration: ${apiIntegrationSuccess}/${results.apiIntegration.length} working`);
    console.log(`Data Flow: ${dataFlowSuccess}/${results.dataFlow.length} validated`);
    console.log(`Real-time Features: ${realTimeSuccess}/${results.realTimeFeatures.length} operational`);
    
    const totalTests = results.staticFiles.length + results.apiIntegration.length + 
                      results.dataFlow.length + results.realTimeFeatures.length;
    const totalSuccess = staticFilesSuccess + apiIntegrationSuccess + 
                        dataFlowSuccess + realTimeSuccess;
    
    const successRate = totalTests > 0 ? (totalSuccess / totalTests * 100).toFixed(1) : 0;
    
    console.log(`Overall Success Rate: ${successRate}%`);
    console.log(`Performance: Load ${avgLoadTime.toFixed(0)}ms | API ${avgApiTime.toFixed(0)}ms`);
    
    const overallSuccess = successRate >= 80 && avgLoadTime < 1000 && avgApiTime < 500;
    console.log(`\nOverall Status: ${overallSuccess ? '✅ PASSED' : '❌ NEEDS ATTENTION'}`);
    
    if (overallSuccess) {
      console.log('🎉 Frontend integration is working correctly!');
      console.log('📱 Dashboard components are operational');
      console.log('🔄 Real-time data updates are functional');
      console.log('⚡ Performance targets are being met');
    }

  } catch (error) {
    console.error('❌ Frontend integration test failed:', error.message);
  }
}

testFrontendIntegration();
