#!/usr/bin/env node

/**
 * Unified System Startup Script for MEV Arbitrage Bot
 * ===================================================
 * 
 * This script provides a complete system startup with:
 * 1. Docker services initialization (Redis, PostgreSQL, InfluxDB)
 * 2. Database connection validation
 * 3. Backend services startup with dependency management
 * 4. Health checks and monitoring
 * 5. Comprehensive error handling and retry logic
 * 6. Production-ready logging and status reporting
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// System configuration
const config = {
  databases: {
    redis: { name: 'Redis', container: 'mev-redis', port: 6379, maxWait: 30000 },
    postgres: { name: 'PostgreSQL', container: 'mev-postgres', port: 5432, maxWait: 45000 },
    influxdb: { name: 'InfluxDB', container: 'mev-influxdb', port: 8086, maxWait: 60000 }
  },
  backend: {
    port: 3001,
    maxStartupTime: 120000,
    healthCheckInterval: 5000
  },
  performance: {
    maxLatency: 1000,
    targetUptime: 99,
    maxMemoryUsage: 100 * 1024 * 1024 // 100MB
  }
};

// Logging functions
function log(message, color = 'reset') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) { log(`✅ ${message}`, 'green'); }
function logError(message) { log(`❌ ${message}`, 'red'); }
function logWarning(message) { log(`⚠️  ${message}`, 'yellow'); }
function logInfo(message) { log(`ℹ️  ${message}`, 'blue'); }
function logStep(step, message) { log(`\n🔄 Step ${step}: ${message}`, 'cyan'); }

// Load environment variables
async function loadEnvironment() {
  try {
    const envPath = path.join(rootDir, '.env');
    const envContent = await fs.readFile(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
          if (!process.env[key]) {
            process.env[key] = value;
          }
        }
      }
    });
    logSuccess('Environment variables loaded');
  } catch (error) {
    logWarning('No .env file found, using default environment');
  }
}

// Check Docker availability
async function checkDockerAvailability() {
  logStep(1, 'Checking Docker Desktop Availability');
  
  try {
    await execAsync('docker info');
    logSuccess('Docker Desktop is running');
    return true;
  } catch (error) {
    logError('Docker Desktop is not running');
    logInfo('Please start Docker Desktop and try again');
    return false;
  }
}

// Start Docker services with proper dependency order
async function startDockerServices() {
  logStep(2, 'Starting Docker Database Services');
  
  try {
    // Check if services are already running
    const { stdout } = await execAsync('docker ps --filter "name=mev-" --format "{{.Names}}"');
    const runningServices = stdout.trim().split('\n').filter(name => name.startsWith('mev-'));
    
    if (runningServices.length > 0) {
      logInfo(`Already running: ${runningServices.join(', ')}`);
    }
    
    // Start services in dependency order: Redis → PostgreSQL → InfluxDB
    logInfo('Starting database containers...');
    await execAsync('docker-compose up -d redis postgres influxdb', { cwd: rootDir });
    logSuccess('Docker containers started');
    
    return true;
  } catch (error) {
    logError(`Failed to start Docker services: ${error.message}`);
    return false;
  }
}

// Wait for service health
async function waitForServiceHealth(serviceName, maxWaitTime = 30000) {
  const service = config.databases[serviceName];
  const startTime = Date.now();
  
  logInfo(`Waiting for ${service.name} to be healthy...`);
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      let healthCommand;
      switch (serviceName) {
        case 'redis':
          healthCommand = 'redis-cli ping';
          break;
        case 'postgres':
          healthCommand = 'pg_isready -U mev_user -d mev_arbitrage_bot';
          break;
        case 'influxdb':
          healthCommand = 'influx ping';
          break;
      }
      
      const { stdout } = await execAsync(`docker exec ${service.container} ${healthCommand}`);
      if (stdout.includes('PONG') || stdout.includes('accepting connections') || stdout.includes('OK')) {
        logSuccess(`${service.name} is healthy`);
        return true;
      }
    } catch (error) {
      // Service not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  logWarning(`${service.name} health check timeout after ${maxWaitTime}ms`);
  return false;
}

// Verify all database services
async function verifyDatabaseServices() {
  logStep(3, 'Verifying Database Service Health');
  
  const healthResults = {};
  
  // Check services in startup order
  for (const [key, service] of Object.entries(config.databases)) {
    healthResults[key] = await waitForServiceHealth(key, service.maxWait);
  }
  
  // Display results
  logInfo('\n📊 Database Service Health Summary:');
  console.log('================================');
  
  let allHealthy = true;
  for (const [key, service] of Object.entries(config.databases)) {
    const status = healthResults[key] ? '✅ Healthy' : '❌ Unhealthy';
    console.log(`${service.name.padEnd(12)}: ${status}`);
    if (!healthResults[key]) allHealthy = false;
  }
  
  return allHealthy;
}

// Test database connections
async function testDatabaseConnections() {
  logStep(4, 'Testing Database Connections');
  
  try {
    logInfo('Running comprehensive database connection test...');
    const { stdout, stderr } = await execAsync('node test-database-connections.js', { cwd: rootDir });
    
    if (stderr && !stderr.includes('Warning')) {
      logWarning(`Database test warnings: ${stderr}`);
    }
    
    // Parse test results
    const lines = stdout.split('\n');
    const results = {
      supabase: lines.some(line => line.includes('✅ Supabase')),
      influxdb: lines.some(line => line.includes('✅ InfluxDB')),
      redis: lines.some(line => line.includes('Redis') && line.includes('✅'))
    };
    
    if (results.supabase && results.influxdb) {
      logSuccess('All database connections verified');
      return true;
    } else {
      logError('Some database connections failed');
      return false;
    }
  } catch (error) {
    logError(`Database connection test failed: ${error.message}`);
    return false;
  }
}

// Start backend services
async function startBackendServices() {
  logStep(5, 'Starting Backend Services');
  
  return new Promise((resolve) => {
    logInfo('Starting enhanced backend with all services...');
    
    const backend = spawn('node', ['enhanced-backend.mjs'], {
      cwd: rootDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    let startupComplete = false;
    let healthChecksPassed = false;
    
    backend.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output);
      
      // Check for startup completion indicators
      if (output.includes('🚀 Enhanced MEV Arbitrage Bot Backend Started') || 
          output.includes('Server running on port')) {
        startupComplete = true;
        logSuccess('Backend services started successfully');
      }
      
      // Check for health check completion
      if (output.includes('All services healthy') || 
          output.includes('System ready')) {
        healthChecksPassed = true;
      }
      
      if (startupComplete && healthChecksPassed) {
        resolve(true);
      }
    });
    
    backend.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('Warning') && !error.includes('DeprecationWarning')) {
        logError(`Backend error: ${error}`);
      }
    });
    
    backend.on('error', (error) => {
      logError(`Failed to start backend: ${error.message}`);
      resolve(false);
    });
    
    // Timeout after max startup time
    setTimeout(() => {
      if (!startupComplete) {
        logWarning('Backend startup timeout - may still be initializing');
        resolve(startupComplete);
      }
    }, config.backend.maxStartupTime);
  });
}

// Perform system health validation
async function performSystemHealthValidation() {
  logStep(6, 'System Health Validation');
  
  try {
    // Test backend API endpoint
    logInfo('Testing backend API health...');
    const { stdout } = await execAsync('curl -s http://localhost:3001/health || echo "API_NOT_READY"');
    
    if (stdout.includes('API_NOT_READY')) {
      logWarning('Backend API not yet ready');
      return false;
    }
    
    logSuccess('Backend API is responding');
    
    // Additional health checks can be added here
    logSuccess('System health validation completed');
    return true;
  } catch (error) {
    logWarning(`Health validation incomplete: ${error.message}`);
    return false;
  }
}

// Display system status
async function displaySystemStatus() {
  logStep(7, 'System Status Summary');
  
  console.log('\n🔗 Service Endpoints:');
  console.log('================================');
  console.log(`Backend API:  http://localhost:${config.backend.port}`);
  console.log(`Redis:        redis://localhost:${config.databases.redis.port}`);
  console.log(`PostgreSQL:   postgresql://mev_user:mev_password@localhost:${config.databases.postgres.port}/mev_arbitrage_bot`);
  console.log(`InfluxDB:     http://localhost:${config.databases.influxdb.port}`);
  console.log(`Supabase:     ${process.env.SUPABASE_URL || 'Not configured'}`);
  
  console.log('\n📋 Container Status:');
  try {
    const { stdout } = await execAsync('docker ps --filter "name=mev-" --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"');
    console.log(stdout);
  } catch (error) {
    logWarning('Could not retrieve container status');
  }
  
  console.log('\n🎯 Performance Targets:');
  console.log('================================');
  console.log(`Max Latency:     <${config.performance.maxLatency}ms`);
  console.log(`Target Uptime:   >${config.performance.targetUptime}%`);
  console.log(`Memory Limit:    <${Math.round(config.performance.maxMemoryUsage / 1024 / 1024)}MB`);
}

// Main startup function
async function startUnifiedSystem() {
  try {
    log(`${colors.bright}🚀 MEV ARBITRAGE BOT - UNIFIED SYSTEM STARTUP${colors.reset}`);
    log('=' .repeat(60));
    
    // Load environment
    await loadEnvironment();
    
    // Step 1: Check Docker availability
    if (!(await checkDockerAvailability())) {
      process.exit(1);
    }
    
    // Step 2: Start Docker services
    if (!(await startDockerServices())) {
      process.exit(1);
    }
    
    // Step 3: Verify database services
    const databasesHealthy = await verifyDatabaseServices();
    
    // Step 4: Test database connections
    const connectionsValid = await testDatabaseConnections();
    
    // Step 5: Start backend services
    const backendStarted = await startBackendServices();
    
    // Step 6: Perform system health validation
    const systemHealthy = await performSystemHealthValidation();
    
    // Step 7: Display system status
    await displaySystemStatus();
    
    // Final status
    if (databasesHealthy && connectionsValid && backendStarted) {
      logSuccess('\n🎉 MEV Arbitrage Bot System Started Successfully!');
      logInfo('All services are running and healthy');
      logInfo('System is ready for trading operations');
      
      if (!systemHealthy) {
        logWarning('Some health checks are still pending - system may need a moment to fully initialize');
      }
    } else {
      logWarning('\n⚠️  System started with some issues:');
      if (!databasesHealthy) logError('- Database services not fully healthy');
      if (!connectionsValid) logError('- Database connections failed');
      if (!backendStarted) logError('- Backend services failed to start');
      
      logInfo('Check the logs above for specific issues');
    }
    
  } catch (error) {
    logError(`System startup failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  log('\n🛑 Shutting down system...');
  process.exit(0);
});

// Start the unified system
startUnifiedSystem();
